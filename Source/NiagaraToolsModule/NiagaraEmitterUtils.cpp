#include "NiagaraEmitterUtils.h"

#include "NiagaraEmitter.h"
#include "NiagaraScriptSource.h"

DEFINE_LOG_CATEGORY_STATIC(LogNigaraEmitterUtils, Log, All);

bool NiagaraEmitterUtils::ExtractDataAndGraph(const UNiagaraEmitter* Emitter, UNiagaraScriptSource*& ScriptSource,
	UNiagaraGraph*& NiagaraGraph)
{
	if (!Emitter)
	{
		UE_LOG(LogNigaraEmitterUtils, Warning, TEXT("Emitter is null"));
		return false;
	}

	const FVersionedNiagaraEmitterData* EmitterData = Emitter->GetEmitterData(Emitter->GetExposedVersion().VersionGuid);
	if (!EmitterData)
	{
		UE_LOG(LogNigaraEmitterUtils, Error, TEXT("Failed to get emitter data."));
		return false;
	}

	ScriptSource = Cast<UNiagaraScriptSource>(EmitterData->GraphSource);
	if (!ScriptSource)
	{
		UE_LOG(LogNigaraEmitterUtils, Error, TEXT("Emitter has no valid script source."));
		return false;
	}

	NiagaraGraph = ScriptSource->NodeGraph;
	return true;
}
