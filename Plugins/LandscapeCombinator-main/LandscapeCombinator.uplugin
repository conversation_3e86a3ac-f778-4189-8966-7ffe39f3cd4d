{"FileVersion": 3, "Version": 6, "VersionName": "0.6", "EngineVersion": "5.4", "FriendlyName": "LandscapeCombinator", "Category": "Other", "MarketplaceURL": "com.epicgames.launcher://ue/marketplace/product/675e54fcb72f42db82125d5573d0667e", "CanContainContent": true, "IsBetaVersion": false, "IsExperimentalVersion": true, "Installed": false, "Modules": [{"Name": "GDALInterface", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "WhitelistPlatforms": ["Win64", "Linux"]}, {"Name": "BuildingFromSpline", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "WhitelistPlatforms": ["Win64", "Linux"]}, {"Name": "Coordinates", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "WhitelistPlatforms": ["Win64", "Linux"]}, {"Name": "FileDownloader", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "WhitelistPlatforms": ["Win64", "Linux"]}, {"Name": "ConcurrencyHelpers", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "WhitelistPlatforms": ["Win64", "Linux"]}, {"Name": "SplineImporter", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "WhitelistPlatforms": ["Win64", "Linux"]}, {"Name": "LandscapeUtils", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "WhitelistPlatforms": ["Win64", "Linux"]}, {"Name": "HeightmapModifier", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "WhitelistPlatforms": ["Win64", "Linux"]}, {"Name": "ImageDownloader", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "WhitelistPlatforms": ["Win64", "Linux"]}, {"Name": "ConsoleHelpers", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "WhitelistPlatforms": ["Win64", "Linux"]}, {"Name": "LandscapeCombinator", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "WhitelistPlatforms": ["Win64", "Linux"]}, {"Name": "OSMUserData", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "WhitelistPlatforms": ["Win64", "Linux"]}, {"Name": "MapboxHelpers", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "WhitelistPlatforms": ["Win64", "Linux"]}, {"Name": "L<PERSON>om<PERSON>", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "WhitelistPlatforms": ["Win64", "Linux"]}, {"Name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "WhitelistPlatforms": ["Win64", "Linux"]}], "Plugins": [{"Name": "GeometryScripting", "Enabled": true}, {"Name": "PCG", "Enabled": true}, {"Name": "StructUtils", "Enabled": true}]}