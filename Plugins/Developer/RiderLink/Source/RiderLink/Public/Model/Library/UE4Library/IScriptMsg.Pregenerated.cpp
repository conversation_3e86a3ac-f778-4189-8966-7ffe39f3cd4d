//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a RdGen v1.13.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------
#include "IScriptMsg.Pregenerated.h"


#include "IScriptMsg_Unknown.Pregenerated.h"

#ifdef _MSC_VER
#pragma warning( push )
#pragma warning( disable:4250 )
#pragma warning( disable:4307 )
#pragma warning( disable:4267 )
#pragma warning( disable:4244 )
#pragma warning( disable:4100 )
#endif

namespace JetBrains {
namespace EditorPlugin {
// companion
// constants
constexpr rd::wstring_view IScriptMsg::header;
// initializer
void IScriptMsg::initialize()
{
}
// primary ctor
// secondary constructor
// default ctors and dtors
IScriptMsg::IScriptMsg()
{
    initialize();
}
// reader
rd::Wrapper<IScriptMsg> IScriptMsg::readUnknownInstance(rd::SerializationCtx& ctx, rd::Buffer & buffer, rd::RdId const& unknownId, int32_t size)
{
    int32_t objectStartPosition = buffer.get_position();
    auto unknownBytes = rd::Buffer::ByteArray(objectStartPosition + size - buffer.get_position());
    buffer.read_byte_array_raw(unknownBytes);
    IScriptMsg_Unknown res{unknownId, unknownBytes};
    return rd::Wrapper<IScriptMsg_Unknown>(std::move(res));
}
// writer
// virtual init
// identify
// getters
// intern
// equals trait
// equality operators
bool operator==(const IScriptMsg &lhs, const IScriptMsg &rhs) {
    if (lhs.type_name() != rhs.type_name()) return false;
    return lhs.equals(rhs);
}
bool operator!=(const IScriptMsg &lhs, const IScriptMsg &rhs){
    return !(lhs == rhs);
}
// hash code trait
size_t IScriptMsg::hashCode() const noexcept
{
    size_t __r = 0;
    return __r;
}
// type name trait
std::string IScriptMsg::type_name() const
{
    return "IScriptMsg";
}
// static type name trait
std::string IScriptMsg::static_type_name()
{
    return "IScriptMsg";
}
// polymorphic to string
std::string IScriptMsg::toString() const
{
    std::string res = "IScriptMsg\n";
    return res;
}
// external to string
std::string to_string(const IScriptMsg & value)
{
    return value.toString();
}
}
}

#ifdef _MSC_VER
#pragma warning( pop )
#endif

