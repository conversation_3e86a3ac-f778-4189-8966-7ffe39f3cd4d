#pragma once

#include "CoreMinimal.h"
#include "UObject/Interface.h"
#include "Dom/JsonObject.h"
#include "Dom/JsonValue.h"

class SAGECORE_API ISageExtensionDelegator
{
public:
	virtual ~ISageExtensionDelegator() = default;
	
	virtual void OnActionApplied(const TSharedPtr<FJsonValue>& ActionDetails) = 0;
	virtual TSharedPtr<FJsonObject> OnQueryRequested(const TSharedPtr<FJsonObject>& QueryRequestObject) = 0;
};
